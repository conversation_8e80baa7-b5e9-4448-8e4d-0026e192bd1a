package com.example.scan.ui.page.screen.aiscanchatlist

import androidx.lifecycle.ViewModel
import com.example.scan.component.common.kermit.debugLog
import com.example.scan.component.common.time.todayStartDateTime
import com.example.scan.feat.objectbox.dao.ChatDao
import com.example.scan.model.aiscan.chat.DbAiScanChat
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

class AiScanChatListViewModel(
  private val chatDao: ChatDao,
) : ViewModel(), ContainerHost<AiScanChatListViewState, Nothing> {

  override val container: Container<AiScanChatListViewState, Nothing> =
    container(AiScanChatListViewState())

  init {
    registerChatList()
  }

  private fun registerChatList() = intent {
    reduce { state.copy(loading = true) }
    chatDao.chatListFlow().collect { chatList ->
      debugLog(tag = "AiScanChatListViewModel") { "chatList: ${chatList.size}" }

      val chats: MutableMap<LocalDateTime, MutableList<DbAiScanChat>> = mutableMapOf()

      chatList.forEach { chat ->
        val day = Instant.fromEpochMilliseconds(chat.createdTimestamp).todayStartDateTime()
        val dailyChats = chats[day] ?: mutableListOf()

        chats[day] = dailyChats.apply { add(chat) }
      }

      reduce { state.copy(chats = chats, loading = false) }
    }
  }

  fun onEditChatTitle(chat: DbAiScanChat, newTitle: String) = intent {
    reduce { state.copy(processing = true) }
    chatDao.upsert(chat.copy(title = newTitle))
    onClearPendingEditChat()
    reduce { state.copy(processing = false) }
  }

  fun onDelete(chat: DbAiScanChat) = intent {
    reduce { state.copy(processing = true) }
    chatDao.delete(chat)
    onClearPendingEditChat()
    reduce { state.copy(processing = false) }
  }

  fun onShowEditSheet(chat: DbAiScanChat) = blockingIntent {
    reduce {
      state.copy(
        pendingEditChat = chat,
        showChatEditSheet = true
      )
    }
  }

  fun onDismissEditSheet() = blockingIntent {
    reduce { state.copy(showChatEditSheet = false) }
  }

  fun onSwitchRenameDialogShow(show: Boolean) = blockingIntent {
    reduce { state.copy(showRenameDialog = show) }
  }

  private suspend fun onClearPendingEditChat() = subIntent {
    reduce { state.copy(pendingEditChat = null) }
  }

}