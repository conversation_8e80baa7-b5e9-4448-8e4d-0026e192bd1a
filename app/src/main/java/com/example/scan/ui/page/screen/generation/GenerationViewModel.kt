@file:Suppress("LocalVariableName")

package com.example.scan.ui.page.screen.generation

import android.annotation.SuppressLint
import android.app.Activity
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.scan.biz.ad.rewarded.AdmobRewardedAdManager
import com.example.scan.biz.ad.rewarded.MaxRewardedAdHelper
import com.example.scan.biz.ad.rewarded.rewardedAdFinishEventFlow
import com.example.scan.biz.remoteconfig.useLegacyAdConfig
import com.example.scan.component.android.toast.showToast
import com.example.scan.component.common.flow.send
import com.example.scan.component.common.guia.previousKey
import com.example.scan.component.common.lyricist.globalStrings
import com.example.scan.feat.store.code.GenerationsStore
import com.example.scan.feat.store.codedecoration.CodeDecorationsStore
import com.example.scan.model.CodeDecoration
import com.example.scan.model.CodeTemplate
import com.example.scan.model.Generation
import com.example.scan.model.barcode.BarcodeGeneration
import com.example.scan.model.needOneTimeUnlock
import com.example.scan.model.qrcode.QrCodeGeneration
import com.example.scan.ui.page.screen.record.RecordScreenNode
import com.example.scan.ui.page.screen.record.RecordScreenType
import com.example.scan.ui.page.screen.record.RefreshFavListStateFlow
import com.example.scan.ui.page.screen.record.RefreshHistoryListStateFlow
import com.roudikk.guia.core.Navigator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

class GenerationViewModel(
  private val generation: Generation,
  private val generationsStoreByFavorite: GenerationsStore.ByFavorite,
  private val generationsStoreByHistory: GenerationsStore.ByHistory,
  private val decorationsStore: CodeDecorationsStore,
  private val maxRewardedAdHelper: MaxRewardedAdHelper,
  private val admobRewardedAdManager: AdmobRewardedAdManager,
) : ViewModel(), ContainerHost<GenerationViewState, Unit> {

  override val container: Container<GenerationViewState, Unit> = container(GenerationViewState())

  init {
    if (useLegacyAdConfig) {
      maxRewardedAdHelper.tryToLoadAd()
    }
  }

  fun onConfigure(
    named: String,
  ) = intent {
    intent { reduceIsFav() }
    reduceDecoration()

    val _named = generation.named ?: named
    reduce {
      state.copy(
        named = TextFieldValue(
          text = _named,
          selection = TextRange(_named.length)
        )
      )
    }
  }

  private fun reduceDecoration() = intent {
    val decoration = decorationsStore.map[generation.id]

    decoration?.let {
      reduce {
        state.copy(
          selectedTemplate = decoration.template,
          selectedContainerBrush = decoration.qrCodeContainerBrush,
          selectedContentBrush = decoration.qrCodeContentBrush
        )
      }
    }
  }

  private suspend fun reduceIsFav() = subIntent {
    val find = generationsStoreByFavorite.list.find { it.id == generation.id }
    reduce { state.copy(isFav = find != null) }
  }

  fun onCodeImageNamed(input: TextFieldValue, navigator: Navigator) = blockingIntent {

    val _generation = when (generation) {
      is QrCodeGeneration -> generation.copy(named = input.text.trim())
      is BarcodeGeneration -> generation.copy(named = input.text.trim())
      else -> return@blockingIntent
    }

    generationsStoreByFavorite.updateAsync(_generation)
    generationsStoreByHistory.updateAsync(_generation)

    val previousNode = navigator
      .backstackKeys
      .getOrNull(1) as? RecordScreenNode

    when (previousNode?.args?.screenType) {
      RecordScreenType.Favorite -> RefreshFavListStateFlow.emit(true)
      RecordScreenType.History -> RefreshHistoryListStateFlow.emit(true)
      null -> {}
    }

    reduce { state.copy(named = input) }
  }

  fun onShowRenamedDialog() = intent {
    reduce { state.copy(showNamedDialog = true) }
  }

  fun onDismissRenamedDialog() = intent {
    reduce { state.copy(showNamedDialog = false) }
  }

  fun doGenerating() = blockingIntent {
    reduce { state.copy(generating = true) }
  }

  fun finishGenerating() = blockingIntent {
    reduce { state.copy(generating = false) }
  }

  fun onSwitchFavState(
    navigator: Navigator
  ) = blockingIntent {
    reduce { state.copy(processing = true) }

    if (state.isFav == null) {
      reduceIsFav()
    }

    val isFav = state.isFav ?: false

    if (isFav) {
      generationsStoreByFavorite.delete(generation)

      val previousNode = navigator.previousKey as? RecordScreenNode

      if (previousNode?.args?.screenType == RecordScreenType.Favorite) {
        RefreshFavListStateFlow.emit(true)
      }

    } else {
      generationsStoreByFavorite.add(generation)
      showToast(globalStrings.savedToFav)
    }

    reduce { state.copy(processing = false, isFav = !isFav) }
  }

  private suspend fun onTemplateSelectInternal(template: CodeTemplate?) = subIntent {
    val newDecoration = CodeDecoration(
      template = template,
      qrCodeContentBrush = null,
      qrCodeContainerBrush = null
    )

    if (newDecoration.isEmpty()) {
      decorationsStore.delete(generation.id)
    } else {
      decorationsStore.set(generation.id, newDecoration)
    }

    reduce {
      state.copy(
        selectedTemplate = template,
        selectedContentBrush = null
      )
    }
  }

  fun onTemplateSelect(template: CodeTemplate) = intent {
    val selectedTemplate = if (template == state.selectedTemplate) null else template

    if (selectedTemplate?.needOneTimeUnlock == true) {
      reduce {
        state.copy(
          showOneTimeTemplateUnlockDialog = true,
          pendingOneTimeUnlockTemplate = selectedTemplate
        )
      }
    } else {
      onTemplateSelectInternal(selectedTemplate)
    }
  }

  private var rewardedAdJob: Job? = null

  fun onUnlockTemplateWithMaxAd(template: CodeTemplate) = intent {

    reduce { state.copy(showOneTimeTemplateUnlockDialog = false) }

    rewardedAdJob?.cancel()
    rewardedAdJob = null
    rewardedAdJob = viewModelScope.launch {
      maxRewardedAdHelper.tryToShowAdWithLoadingDialog()
      rewardedAdFinishEventFlow.take(1).collectLatest { rewardedAdFinish ->
        if (rewardedAdFinish) {
          subIntent {
            intent {
              reduce {
                state.copy(
                  showOneTimeTemplateUnlockDialog = false,
                  pendingOneTimeUnlockTemplate = null
                )
              }
            }
            onTemplateSelectInternal(template)
          }
        } else {
          subIntent {
            reduce { state.copy(showOneTimeTemplateUnlockDialog = true) }
          }
        }
      }
    }


  }

  @SuppressLint("StaticFieldLeak")
  private var showRewardedAdContainer: Activity? = null
  fun onUnlockTemplateWithAdmob(activity: Activity, template: CodeTemplate) = intent {
    showRewardedAdContainer = activity

    reduce { state.copy(adLoading = true, showOneTimeTemplateUnlockDialog = false) }

    admobRewardedAdManager.tryToShowAd(activity)

    val earnedRewardSuccessful =
      admobRewardedAdManager.adEarnedRewardEventFlow.firstOrNull()

    showRewardedAdContainer = null
    if (earnedRewardSuccessful == true) {
      reduce {
        state.copy(
          adLoading = false,
          showOneTimeTemplateUnlockDialog = false,
          pendingOneTimeUnlockTemplate = null
        )
      }
      onTemplateSelectInternal(template)
    } else {
      reduce { state.copy(adLoading = false, showOneTimeTemplateUnlockDialog = true) }
    }
  }

  fun onDismissOneTimeUnlockTemplateDialog() = intent {
    reduce {
      state.copy(
        showOneTimeTemplateUnlockDialog = false,
        pendingOneTimeUnlockTemplate = null
      )
    }
  }

  fun registerAdmobRewardedAdEventFlow(lifecycleScope: CoroutineScope) {
    admobRewardedAdManager.adLoadingStateEventFlow.onEach {
      when (it) {
        AdmobRewardedAdManager.AdLoadingStateEvent.FailedToLoad,
        AdmobRewardedAdManager.AdLoadingStateEvent.TimeOut -> intent {
          reduce { state.copy(adLoading = false) }
          showRewardedAdContainer = null
          admobRewardedAdManager.adEarnedRewardEventFlow.send(false)
          showToast(globalStrings.rewardedAdLoadingFailedTips)
        }

        AdmobRewardedAdManager.AdLoadingStateEvent.Loaded -> {
          showRewardedAdContainer?.let { containerActivity ->
            admobRewardedAdManager.tryToShowAd(containerActivity, null)
          }
        }
      }
    }.launchIn(lifecycleScope)

    admobRewardedAdManager.adShowStateEventFlow.onEach {
      when (it) {
        AdmobRewardedAdManager.AdShowStateEvent.FailedToShow,
        AdmobRewardedAdManager.AdShowStateEvent.Finish -> {
        }

        AdmobRewardedAdManager.AdShowStateEvent.Showing ->
          intent { reduce { state.copy(adLoading = false) } }
      }
    }.launchIn(lifecycleScope)
  }

  fun preloadAdmobRewardedAd(activity: Activity) {
    admobRewardedAdManager.tryToLoadAd(activity)
  }
}
