package com.example.scan.ui.page.screen.scancoderesult

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.scan.component.android.toast.showToast
import com.example.scan.component.common.lyricist.globalStrings
import com.example.scan.feat.store.code.ScanResultsStore
import com.example.scan.model.qrcode.CodeScanResult
import com.example.scan.ui.page.screen.record.RecordScreenNode
import com.example.scan.ui.page.screen.record.RecordScreenType
import com.example.scan.ui.page.screen.record.RefreshFavListStateFlow
import com.roudikk.guia.core.Navigator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

class ScanCodeResultViewModel(
  private val codeScanResult: CodeScanResult,
  private val scanResultsStoreByFavorite: ScanResultsStore.ByFavorite
) :
  ViewModel(),
  ContainerHost<ScanCodeResultViewState, ScanCodeResultSideEffect> {

  override val container: Container<ScanCodeResultViewState, ScanCodeResultSideEffect> =
    container(ScanCodeResultViewState())

  init {
    onConfigure()
  }

  fun onConfigure() = intent {
    intent { reduceIsFav() }
    reduce { state.copy(codeScanResult = codeScanResult) }
  }

  private val onShareBlock = MutableStateFlow {}
  fun onConfigureShare(onShare: () -> Unit) {
    onShareBlock.update { onShare }
  }

  fun onShare() {
    viewModelScope.launch {
      onShareBlock.first().invoke()
    }
  }

  private suspend fun reduceIsFav() = subIntent {
    val find = scanResultsStoreByFavorite.list.find { it.id == codeScanResult.id }
    reduce { state.copy(isFav = find != null) }
  }

  fun onSwitchFavState(
    navigator: Navigator
  ) = blockingIntent {
    reduce { state.copy(processing = true) }

    if (state.isFav == null) {
      reduceIsFav()
    }

    val isFav = state.isFav ?: false

    if (isFav) {
      scanResultsStoreByFavorite.delete(codeScanResult)

      val previousNode = navigator
        .backstackKeys
        .getOrNull(1) as? RecordScreenNode

      if (previousNode?.args?.screenType == RecordScreenType.Favorite) {
        RefreshFavListStateFlow.emit(true)
      }

    } else {
      scanResultsStoreByFavorite.add(codeScanResult)
      showToast(globalStrings.savedToFav)
    }

    reduce { state.copy(processing = false, isFav = !isFav) }
  }
}
