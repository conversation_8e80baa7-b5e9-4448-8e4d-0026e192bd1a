package com.example.scan.ui.page.screen.home

import androidx.lifecycle.ViewModel
import com.example.scan.component.common.kermit.debugLog
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

var HomeStateFlow: StateFlow<HomeViewState>? = null

class HomeViewModel(
  subScreen: HomeSubScreen,
) : ViewModel(), ContainerHost<HomeViewState, HomeSideEffect> {
  override val container: Container<HomeViewState, HomeSideEffect> = container(HomeViewState())

  init {
    onNavigationSelected(subScreen)
    HomeStateFlow = container.stateFlow
  }

  fun onNavigationSelected(subScreen: HomeSubScreen) = blockingIntent {
    debugLog("onNavigationSelected: $subScreen")
    reduce { state.copy(selectedSubScreen = subScreen) }
  }

  fun onSwitchMenuExpanded(expanded: Boolean) = blockingIntent {
    reduce { state.copy(menuExpanded = expanded) }
  }
}