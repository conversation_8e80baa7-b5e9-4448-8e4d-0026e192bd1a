package com.example.scan.ui.page.screen.home.scan

import android.content.Context
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.scan.R
import com.example.scan.biz.ad.AdLoadingDialogShowStateFlow
import com.example.scan.biz.ad.interstitial.MaxInterstitialAdHelper
import com.example.scan.biz.remoteconfig.RealRemoteConfig
import com.example.scan.component.android.compress.compress
import com.example.scan.component.android.toast.showToast
import com.example.scan.component.android.vibration.vibrate
import com.example.scan.component.common.lyricist.globalStrings
import com.example.scan.feat.media.SimpleAudioPlayer
import com.example.scan.feat.network.api.ScannerUploadApi
import com.example.scan.feat.network.api.upload
import com.example.scan.feat.network.model.ScanPictureUploadRequest
import com.example.scan.feat.network.safeFirst
import com.example.scan.feat.objectbox.dao.ChatDao
import com.example.scan.feat.store.code.ScanResultsStore
import com.example.scan.feat.store.credit.CreditStore
import com.example.scan.model.aiscan.chat.DbAiScanChat
import com.example.scan.model.aiscan.chatmessage.AiScanChatMessageContentType
import com.example.scan.model.aiscan.chatmessage.AiScanChatMessageType
import com.example.scan.model.aiscan.chatmessage.DbAiScanChatMessage
import com.example.scan.model.qrcode.CodeScanResult
import com.example.scan.model.qrcode.QrCodeType
import com.example.scan.model.qrcodecontent.CalendarEvent
import com.example.scan.model.qrcodecontent.ContactInfo
import com.example.scan.model.qrcodecontent.DriverLicense
import com.example.scan.model.qrcodecontent.Email
import com.example.scan.model.qrcodecontent.GeoPoint
import com.example.scan.model.qrcodecontent.Phone
import com.example.scan.model.qrcodecontent.Sms
import com.example.scan.model.qrcodecontent.WiFi
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

class ScanViewModel(
  private val context: Context,
  private val scannerUploadApi: ScannerUploadApi,
  private val scanResultsStoreByHistory: ScanResultsStore.ByHistory,
  private val interstitialAdHelper: MaxInterstitialAdHelper,
  private val chatDao: ChatDao,
  private val creditStore: CreditStore,
  private val remoteConfig: RealRemoteConfig,
) : ViewModel(), ContainerHost<ScanViewState, ScanSideEffect> {

  private val audioPlayer = SimpleAudioPlayer(context)

  override val container: Container<ScanViewState, ScanSideEffect> =
    container(ScanViewState())

  init {
    registerCredits()
  }

  private fun registerCredits() = intent {
    creditStore.creditsFlow.collect {
      reduce { state.copy(credits = it) }
    }
  }

  fun resetFlashlightState() = intent {
    reduce { state.copy(enabledFlashlight = state.barcodeCamera.flashLightOn()) }
  }

  fun onSwitchScanMode(scanMode: ScanMode) = blockingIntent {
    reduce { state.copy(scanMode = scanMode) }
  }

  fun onSwitchFlashlight() = intent {
    reduce { state.copy(enabledFlashlight = !state.enabledFlashlight) }
  }

  private val handlingBarcode = MutableStateFlow(false)
  fun onScanned(
    context: Context,
    qrCodeType: QrCodeType,
    barcode: Barcode
  ) = intent {
    if (state.scanMode != ScanMode.QrCode) return@intent
    if (!state.enabledScan) return@intent
    if (qrCodeType == QrCodeType.UNKNOWN) return@intent
    if (handlingBarcode.first()) return@intent

    if (AdLoadingDialogShowStateFlow.first()) return@intent
    if (interstitialAdHelper.interstitialAdShowState.first()) return@intent

    handlingBarcode.emit(true)

    val codeScanResult = when (qrCodeType) {

      QrCodeType.CONTACT_INFO -> {
        val contactInfo =
          barcode.contactInfo?.let { ContactInfo.buildViaBarcodeContactInfo(it) }
        barcode.rawValue
        CodeScanResult(
          type = qrCodeType,
          contactInfo = contactInfo,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.EMAIL -> {
        val email = barcode.email?.let { Email.buildViaBarcodeEmail(it) }

        CodeScanResult(
          type = qrCodeType,
          email = email,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.ISBN -> {
        val isbn = barcode.displayValue

        CodeScanResult(
          type = qrCodeType,
          isbn = isbn,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.PHONE -> {
        val phone = barcode.phone?.let { Phone.buildViaBarcodePhone(it) }

        CodeScanResult(
          type = qrCodeType,
          phone = phone,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.PRODUCT -> {
        val product = barcode.displayValue

        CodeScanResult(
          type = qrCodeType,
          product = product,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.SMS -> {
        val sms = barcode.sms?.let { Sms.buildViaBarcodeSms(it) }

        CodeScanResult(
          type = qrCodeType,
          sms = sms,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.TEXT -> {
        val text = barcode.displayValue

        CodeScanResult(
          type = qrCodeType,
          text = text,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.URL -> {
        val url = barcode.url?.url

        CodeScanResult(
          type = qrCodeType,
          url = url,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.WIFI -> {
        val wifi = barcode.wifi?.let { WiFi.buildViaBarcodeWifi(it) }

        CodeScanResult(
          type = qrCodeType,
          wifi = wifi,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.GEO -> {
        val geoPoint = barcode.geoPoint?.let { GeoPoint.buildViaBarcodeGeoPoint(it) }

        CodeScanResult(
          type = qrCodeType,
          geo = geoPoint,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.CALENDAR_EVENT -> {
        val calendarEvent =
          barcode.calendarEvent?.let { CalendarEvent.buildViaBarcodeCalendarEvent(it) }

        CodeScanResult(
          type = qrCodeType,
          calendarEvent = calendarEvent,
          rawValue = barcode.rawValue
        )
      }

      QrCodeType.DRIVER_LICENSE -> {
        val driverLicense =
          barcode.driverLicense?.let { DriverLicense.buildViaBarcodeDriverLicense(it) }

        CodeScanResult(
          type = qrCodeType,
          driverLicense = driverLicense,
          rawValue = barcode.rawValue
        )
      }

      else -> {
        null
      }
    }

    codeScanResult?.let {
      audioPlayer.playAudio(R.raw.scan_finish_audio)
      context.vibrate(24, 45)
      scanResultsStoreByHistory.addAsync(it)
      postSideEffect(ScanSideEffect.NavToQrCodeResult(codeScanResult))
    }

    delay(2000)
    handlingBarcode.emit(false)
  }

  fun onTakePicture(context: Context) = intent {
    reduce { state.copy(takingPicture = true) }

    runCatching {
      state.barcodeCamera.takePicture(
        context = context,
        onSuccess = { uri ->
          handleTakePictureSuccess(context, uri)
        },
        onError = {
          handleTakePictureError()
        }
      )
    }.onFailure {
      it.printStackTrace()
      delay(1000)
      handleTakePictureError()
    }
  }

  private fun handleTakePictureSuccess(context: Context, uri: Uri) = intent {
    reduce { state.copy(takePictureUri = uri) }

    onSelectImage(context = context, uri = uri)

    viewModelScope.launch {
      intent {
        delay(500)
        reduce { state.copy(takingPicture = false) }
      }
    }
  }

  private fun handleTakePictureError() = intent {
    reduce { state.copy(takingPicture = false) }
    showToast(globalStrings.takingPictureError)
  }

  fun onSelectImage(
    context: Context,
    uri: Uri?,
  ) = intent {
    when (state.scanMode) {
      ScanMode.QrCode -> onSelectImageToScanQrCode(context, uri)
      ScanMode.Object -> onSelectImageToObject(context, uri)
      ScanMode.AiScan -> onSelectImageToAiScan(context, uri)
    }
  }

  private suspend fun onSelectImageToScanQrCode(
    context: Context,
    uri: Uri?,
  ) = subIntent {
    reduce { state.copy(enabledScan = true) }

    if (uri == null) {
      return@subIntent
    }

    val image = InputImage.fromFilePath(context, uri)
    state.barcodeCamera.processInputImage(image) { barcode ->
      barcode ?: return@processInputImage
      onScanned(context, QrCodeType.rawValueOf(barcode.valueType), barcode)
    }
  }

  private suspend fun onSelectImageToObject(
    context: Context,
    uri: Uri?,
  ) = subIntent {
    reduce { state.copy(processing = true) }

    val pendingUploadFile =
      uri?.let { context.contentResolver.openInputStream(uri)?.compress(context) }

    if (pendingUploadFile == null) {
      reduce { state.copy(processing = false) }
      onClearTakePictureUri()
      return@subIntent
    }

    val uploadedFileUrl = scannerUploadApi.upload(
      request = ScanPictureUploadRequest(),
      file = pendingUploadFile
    ).safeFirst()?.jsonData?.data?.file_url

    reduce { state.copy(processing = false) }

    if (uploadedFileUrl != null) {
      postSideEffect(ScanSideEffect.NavToObjectResult(uploadedFileUrl, uri))
    } else {
      onClearTakePictureUri()
      showToast(globalStrings.networkError)
    }
  }

  private suspend fun onSelectImageToAiScan(
    context: Context,
    uri: Uri?,
  ) = subIntent {
    if (creditStore.credits - remoteConfig.creditsConfig.consumeByChat < 0) {
      postSideEffect(ScanSideEffect.NotEnoughCredits)
      return@subIntent
    }

    reduce { state.copy(processing = true) }

    val pendingUploadFile =
      uri?.let { context.contentResolver.openInputStream(uri)?.compress(context) }

    if (pendingUploadFile == null) {
      reduce { state.copy(processing = false) }
      onClearTakePictureUri()
      return@subIntent
    }

    val uploadedFileUrl = scannerUploadApi.upload(
      request = ScanPictureUploadRequest(
        uploadStoreType = ScanPictureUploadRequest.UPLOAD_STORE_INFINITE
      ),
      file = pendingUploadFile
    ).safeFirst()?.jsonData?.data?.file_url

    if (uploadedFileUrl != null) {
      val chatId = createAiScanChat(uploadedFileUrl)
      postSideEffect(ScanSideEffect.NavToAiScanResult(chatId))
    } else {
      onClearTakePictureUri()
      showToast(globalStrings.networkError)
    }

    reduce { state.copy(processing = false) }
  }

  private suspend fun createAiScanChat(url: String): Long {
    val chatMessage = DbAiScanChatMessage(
      content = url,
      contentType = AiScanChatMessageContentType.ImageUrlOnly.rawTypeValue,
      messageType = AiScanChatMessageType.SentByUser.rawTypeValue,
    )

    val chat = DbAiScanChat(
      image = url,
    ).apply {
      messageList.add(chatMessage)
    }

    chatDao.upsert(chat)

    return chat.chatId
  }

  fun onGallerySelecting() = intent {
    reduce { state.copy(enabledScan = false) }
  }

  fun onReleaseCamera() = blockingIntent {
    withContext(Dispatchers.Main.immediate) {
      state.barcodeCamera.releaseCamera()
    }
    reduce { state.copy(enabledFlashlight = false, takePictureUri = null) }
  }

  fun onClearTakePictureUri() = intent {
    reduce { state.copy(takePictureUri = null) }
  }

  private fun getFileMimeType(context: Context, uri: Uri?) =
    uri?.let { context.contentResolver.getType(it) }

  override fun onCleared() {
    audioPlayer.stopAudio()
    super.onCleared()
  }
}