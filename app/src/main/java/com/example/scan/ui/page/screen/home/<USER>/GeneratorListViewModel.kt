package com.example.scan.ui.page.screen.home.generator

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import com.example.scan.feat.store.code.GenerationsStore
import com.example.scan.model.qrcode.QrCodeGeneration
import com.example.scan.ui.component.item.qrcode.QrCodeGenerationType
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

class GeneratorListViewModel(
  private val generationStoreByHistory: GenerationsStore.ByHistory
) : ViewModel(), ContainerHost<GeneratorListViewState, GeneratorListSideEffect> {

  override val container: Container<GeneratorListViewState, GeneratorListSideEffect> =
    container(GeneratorListViewState())

  fun onWebsiteInputChange(input: TextFieldValue) = blockingIntent {
    reduce { state.copy(websiteInput = input) }
  }

  fun onGenerate(type: QrCodeGenerationType, content: String) = intent {
//    reduce { state.copy(processing = true) }
    val qrCodeGeneration = QrCodeGeneration(type, content)
    generationStoreByHistory.add(qrCodeGeneration)
//    reduce { state.copy(processing = false) }
    postSideEffect(GeneratorListSideEffect.NavToGeneration(qrCodeGeneration))
  }

  fun onGeneratorListTypeSwitch(
    selectType: GeneratorListType
  ) = intent {
    reduce { state.copy(generatorListType = selectType) }
  }
}