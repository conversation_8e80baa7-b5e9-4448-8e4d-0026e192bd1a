package com.example.scan.ui.page.screen.aiscanchat

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import com.example.scan.biz.remoteconfig.RealRemoteConfig
import com.example.scan.component.android.toast.showToast
import com.example.scan.component.common.coroutine.AppCoroutineScope
import com.example.scan.component.common.guia.GlobalNavigator
import com.example.scan.component.common.kermit.debugLog
import com.example.scan.component.common.lyricist.globalStrings
import com.example.scan.feat.network.Transceiver
import com.example.scan.feat.network.api.aiscanchat.AiScanChatApi
import com.example.scan.feat.network.api.aiscanchat.AiScanChatRequest
import com.example.scan.feat.network.api.aiscanchat.Reading
import com.example.scan.feat.network.api.aiscanchat.readMessage
import com.example.scan.feat.objectbox.dao.ChatDao
import com.example.scan.feat.store.credit.CreditStore
import com.example.scan.model.aiscan.chatmessage.AiScanChatMessageContentType
import com.example.scan.model.aiscan.chatmessage.AiScanChatMessageType
import com.example.scan.model.aiscan.chatmessage.DbAiScanChatMessage
import com.example.scan.ui.page.dialog.rewardedcredits.RewardedCreditsDialogNode
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import io.ktor.http.isSuccess
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.blockingIntent
import org.orbitmvi.orbit.viewmodel.container

private val currentActiveChatId = MutableStateFlow<Long?>(null)

class AiScanChatViewModel(
  private val chatId: Long,
  private val chatDao: ChatDao,
  private val creditStore: CreditStore,
  private val aiScanChatApi: AiScanChatApi,
  private val appCoroutineScope: AppCoroutineScope,
  private val remoteConfig: RealRemoteConfig,
) : ViewModel(), ContainerHost<AiScanChatViewState, Nothing> {

  override val container: Container<AiScanChatViewState, Nothing> = container(AiScanChatViewState())

  init {
    currentActiveChatId.update { chatId }
    registerChatObserver()
    registerCredits()
    initChatIfNeeded()
  }

  private fun registerChatObserver() = intent {
    chatDao.query(chatId).collect {
      reduce { state.copy(chat = it, messages = it?.messageList?.reversed() ?: emptyList()) }
    }
  }

  private fun initChatIfNeeded() = intent {
    if (chatDao.query(chatId).first()?.description.isNullOrEmpty()) {
      onSendFirst()
    }
  }


  private fun registerCredits() = intent {
    creditStore.creditsFlow.collect {
      reduce { state.copy(credits = it) }
    }
  }

  fun onSwitchChatMessageActionSheetShow(
    message: DbAiScanChatMessage?,
    show: Boolean
  ) = blockingIntent {
    reduce {
      state.copy(
        showChatMessageActionSheet = show,
        handlingChatMessageInActionSheet = message
      )
    }
  }

  fun onDeleteMessage(message: DbAiScanChatMessage) = blockingIntent {
    val chat = chatDao.query(chatId).first() ?: return@blockingIntent

    val messageList = chat.messageList
    val findMsgIndex = messageList.indexOfFirst { it.messageId == message.messageId }

    val newDbMessages = messageList.apply {
      removeAt(findMsgIndex)
    }

    chatDao.upsert(chat.apply { this.messageList = newDbMessages })
  }

  fun onInputChange(inputContent: TextFieldValue) = blockingIntent {
    reduce { state.copy(inputContent = inputContent) }
  }

  private fun onSendFirst() = intent {
    if (creditStore.credits - remoteConfig.creditsConfig.consumeByChat < 0) {
      GlobalNavigator.transaction { pop() }
      return@intent
    }
    val chat = chatDao.query(chatId).first() ?: return@intent

    val dbMessages = chat.messageList
    val apiMessages = dbMessages.map { it.toApiPojo() }

    val httpStatement = aiScanChatApi.sendMessage(Transceiver(AiScanChatRequest(apiMessages)))

    intent { reduce { state.copy(showPendingBubble = true, sendEnabled = false) } }

    httpStatement.readMessage(
      lifecycleCoroutineScope = appCoroutineScope,
      onResponseStatus = { status ->
        debugLog(tag = "AiScanChatViewModel") { "$status" }
        if (status.isSuccess()) {
          onConsumeCredits(remoteConfig.creditsConfig.consumeByChat)
        }
      })
      .handleReadMessage(
        neeUpdateDescription = true,
        appCoroutineScope
      )
  }

  fun onSend() = intent {
    if (creditStore.credits - remoteConfig.creditsConfig.consumeByChat < 0) {
      GlobalNavigator.transaction {
        push(RewardedCreditsDialogNode())
      }
      return@intent
    }

    val pendingSendContent = state.inputContent.text.trim()
    onInputChange(TextFieldValue())

    val chat = chatDao.query(chatId).first() ?: return@intent

    val newMessage = DbAiScanChatMessage(
      content = pendingSendContent,
      contentType = AiScanChatMessageContentType.Standard.rawTypeValue,
      messageType = AiScanChatMessageType.SentByUser.rawTypeValue
    )

    val newDbMessages = chat.messageList.apply {
      add(newMessage)
    }
    chatDao.upsert(chat.apply { messageList = newDbMessages })

    val apiMessages = newDbMessages.map { it.toApiPojo() }

    val httpStatement = aiScanChatApi.sendMessage(Transceiver(AiScanChatRequest(apiMessages)))

    intent { reduce { state.copy(showPendingBubble = true, sendEnabled = false) } }

    httpStatement.readMessage(
      lifecycleCoroutineScope = appCoroutineScope,
      onResponseStatus = { status ->
        debugLog(tag = "AiScanChatViewModel") { "$status" }
        if (status.isSuccess()) {
          onConsumeCredits(remoteConfig.creditsConfig.consumeByChat)
        }
      })
      .handleReadMessage(
        neeUpdateDescription = false,
        appCoroutineScope
      )
  }

  private fun Flow<Reading>.handleReadMessage(
    neeUpdateDescription: Boolean,
    launchScope: CoroutineScope,
  ) {
    onEach { reading ->
      when (reading) {
        is Reading.ReadMessage -> {
          val streamingContent = reading.content

          debugLog(tag = "AiScanChatViewModel") { "streamingContent: $streamingContent" }

          if (neeUpdateDescription) {
            val chat = chatDao.query(chatId).first() ?: return@onEach

            val copy = chat.copy(description = streamingContent)
            chatDao.upsert(copy)
          }

          val chat = chatDao.query(chatId).first() ?: return@onEach

          val dbMessages = chat.messageList

          val msg = dbMessages.lastOrNull() ?: return@onEach
          if (msg.isSentByUser()) {
            debugLog(tag = "AiScanChatViewModel") { "isSentByUser" }
            dbMessages.add(
              DbAiScanChatMessage(
                content = streamingContent,
                contentType = AiScanChatMessageContentType.Standard.rawTypeValue,
                messageType = AiScanChatMessageType.SentByAI.rawTypeValue
              )
            )
          } else {
            debugLog(tag = "AiScanChatViewModel") { "isSentByUser not" }
            dbMessages[dbMessages.lastIndex] = msg.copy(
              id = 0,
              content = streamingContent,
              contentType = AiScanChatMessageContentType.Standard.rawTypeValue,
              messageType = AiScanChatMessageType.SentByAI.rawTypeValue
            )
          }

          chatDao.upsert(chat.apply {
            this.messageList = dbMessages
          })

          try {
            intent {
              reduce { state.copy(showPendingBubble = false, sendEnabled = reading.isFinished) }
            }
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }

        Reading.Error -> {
          val chat = chatDao.query(chatId).first()
          if ((chat?.messageList?.size ?: 0) <= 2) {
            chat?.let { chatDao.delete(chat) }

            showToast(globalStrings.imageScanningError)
            if (GlobalNavigator.navigator?.currentKey is AiScanChatNode
              && currentActiveChatId.first() == chatId
            ) {
              GlobalNavigator.transaction {
                pop()
              }
            }
          } else {
            showToast(globalStrings.anExceptionOccurred)
          }
          try {
            intent {
              reduce { state.copy(showPendingBubble = false, sendEnabled = true) }
            }
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }

        Reading.Crash -> {
          showToast(globalStrings.checkYourNetworkConnectivity)
          try {
            intent { reduce { state.copy(showPendingBubble = false, sendEnabled = true) } }
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }
      }
    }.launchIn(launchScope)
  }

  private fun onConsumeCredits(consume: Long) {
    creditStore.addCredits(-consume)
  }

  override fun onCleared() {
    currentActiveChatId.update { null }
    super.onCleared()
  }
}