package com.example.scan.biz.notification

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.ContextCompat
import com.example.scan.AppActivity
import com.example.scan.biz.analytic.logEventRecord
import com.example.scan.biz.rating.RatingHelper
import com.example.scan.component.android.context.isPermissionGranted
import com.example.scan.component.android.toast.showToast
import com.example.scan.component.common.guia.GlobalNavigator
import com.example.scan.component.common.guia.ScreenNode
import com.example.scan.component.common.kermit.debugLog
import com.example.scan.component.common.time.now
import com.example.scan.configureFirebaseMessagingTopicIfNeeded
import com.example.scan.ui.page.screen.home.HomeScreenNode
import com.example.scan.ui.page.screen.home.HomeStateFlow
import com.example.scan.ui.page.screen.home.HomeSubScreen
import com.example.scan.ui.page.screen.splash.SplashScreenNode
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.delay
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

private const val TAG = "NotificationPermissionRequester"

class NotificationPermissionRequester(
  private val mmkv: MMKV,
  private val fixedNotificationHelper: FixedNotificationHelper
) {

  private var requestTimes
    get() = mmkv.decodeInt("${TAG}_requestTimes", 0)
    set(value) {
      mmkv.encode("${TAG}_requestTimes", value)
    }

  private var firstRequestInstant: Instant
    get() = mmkv.decodeLong("${TAG}_firstRequestInstant", 0L).let(Instant::fromEpochSeconds)
    set(value) {
      mmkv.encode("${TAG}_firstRequestInstant", value.epochSeconds)
    }

  private var latestRequestInstant: Instant
    get() = mmkv.decodeLong("${TAG}_latestRequestInstant", 0L).let(Instant::fromEpochSeconds)
    set(value) {
      mmkv.encode("${TAG}_latestRequestInstant", value.epochSeconds)
    }

  private var requestPermissionLauncher: ActivityResultLauncher<String>? = null

  @Composable
  fun RegisterRequesterIfNeeded(
    activity: Activity,
    navigator: Navigator,
  ) {
    debugLog(tag = TAG) { "registerRequesterIfNeeded" }

    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

    val currentNode = navigator.currentKey

    val isInForeground =
      (activity as? AppActivity)?.isInForegroundStateFlow?.collectAsState()?.value == true

    LaunchedEffect(
      currentNode,
      HomeStateFlow?.collectAsState()?.value?.selectedSubScreen,
      isInForeground
    ) {
      when {
        currentNode is HomeScreenNode -> {
          if (RatingHelper.willBeRatting()) return@LaunchedEffect

          val homeSelectedSubScreen = HomeStateFlow?.value?.selectedSubScreen

          if (homeSelectedSubScreen in arrayOf(HomeSubScreen.ScanCode, HomeSubScreen.ScanObject)) {
            if (activity.isPermissionGranted(Manifest.permission.CAMERA)) {
              tryToRequestIfNeeded(activity)
            }
          } else {
            tryToRequestIfNeeded(activity)
          }
        }

        currentNode is ScreenNode && currentNode !is SplashScreenNode -> {
          delay(100)
          if (isInForeground) {
            tryToRequestIfNeeded(activity)
          }
        }
      }
    }
  }

  fun tryToRequestIfNeeded(
    activity: Activity,
  ) {
    debugLog(tag = TAG) { "tryToRequestIfNeeded requestTimes:$requestTimes" }

    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

    if (!shouldShowRequest()) return

    when (requestTimes) {
      0 -> {
        showSystemRequester(activity)
      }

      1 -> {
        showCustomRequester(activity, true)
      }

      in 2..9 -> {
        showCustomRequester(activity, false)
      }

      else -> {}
    }
  }

  @RequiresApi(Build.VERSION_CODES.TIRAMISU)
  fun showSystemRequester(
    activity: Activity,
    enabledRequestCounterIncrement: Boolean = true
  ) {
    debugLog(tag = TAG) { "showSystemRequester()" }
    if (ContextCompat.checkSelfPermission(
        activity,
        Manifest.permission.POST_NOTIFICATIONS
      ) != PackageManager.PERMISSION_GRANTED
    ) {
      requestPermissionLauncher?.run {
        try {
          if (enabledRequestCounterIncrement) {
            requestCounterIncrement()
          }
          launch(Manifest.permission.POST_NOTIFICATIONS)
        } catch (e: Exception) {
          Firebase.crashlytics.recordException(e)
          e.printStackTrace()
        }
      }
    }
  }

  fun showCustomRequester(
    activity: Activity,
    useSystemPermissionDialog: Boolean
  ) {
    if (ContextCompat.checkSelfPermission(
        activity,
        Manifest.permission.POST_NOTIFICATIONS
      ) != PackageManager.PERMISSION_GRANTED
    ) {
      GlobalNavigator.tryTransaction {
        push(NotificationPermissionRequestDialogNode(useSystemPermissionDialog = useSystemPermissionDialog))
      }
    }
  }

  fun showDefaultFixedNotification() {
    fixedNotificationHelper.startNoti(null)
  }

  fun tryToShowDefaultFixedNotification(context: Context) {
    if (ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.POST_NOTIFICATIONS
      ) == PackageManager.PERMISSION_GRANTED
    ) {
      showDefaultFixedNotification()
    }
  }

  fun registerPermissionResult(
    activity: AppActivity
  ) {
    requestPermissionLauncher =
      activity.registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
        if (isGranted) {
          configureFirebaseMessagingTopicIfNeeded(activity)
//          showDefaultFixedNotification()
          if (requestTimes == 1) {
            logEventRecord("noti_permission_request_1s_success")
            debugLog { "noti_permission_request_1s_success" }
          } else {
            logEventRecord("noti_permission_request_success")
            debugLog { "noti_permission_request_success" }
          }
        } else {
          debugLog { "noti_permission_request_fail" }
        }
      }
  }

  fun requestCounterIncrement() {
    val now = Clock.now()

    if (firstRequestInstant.epochSeconds == 0L) {
      firstRequestInstant = now
    }
    latestRequestInstant = now
    requestTimes++

    if (requestTimes == 1) {
      logEventRecord("noti_permission_request_1st")
      debugLog { "noti_permission_request_1st" }
    } else {
      logEventRecord("noti_permission_request")
      debugLog { "noti_permission_request" }
    }
  }

  private fun shouldShowRequest(): Boolean {
    if (requestTimes == 0) return true

    val now = Clock.now()

    val timeSinceLastRequest = now - latestRequestInstant
    val timeSinceFirstRequest = now - firstRequestInstant

    when {
      requestTimes == 1 && timeSinceLastRequest > 2.minutes -> {
        // 第二次弹窗，间隔需要大于2分钟
        return true
      }

      timeSinceFirstRequest <= 24.hours && timeSinceLastRequest > 1.hours -> {
        // 24小时内，间隔需要大于1小时
        return true
      }

      timeSinceFirstRequest > 24.hours && timeSinceLastRequest > 12.hours -> {
        // 24小时后，间隔需要大于12小时
        return true
      }
    }

    return false
  }
}