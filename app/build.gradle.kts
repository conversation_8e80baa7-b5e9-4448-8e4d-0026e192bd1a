import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import java.util.Properties

plugins {
  alias(libs.plugins.compose.compiler)
  alias(libs.plugins.androidApplication)
  alias(libs.plugins.jetbrainsKotlinAndroid)
  alias(libs.plugins.kotlin.parcelize)
  alias(libs.plugins.kotlin.serialization)
  alias(libs.plugins.ktorfit)
  alias(libs.plugins.ksp)
  alias(libs.plugins.google.services)
  alias(libs.plugins.firebase.crashlytics)
  id("kotlin-kapt")
  id("io.objectbox")
}

apply(from = "${rootProject.projectDir}/conf4build/copyProject.gradle")
apply(from = "${rootProject.projectDir}/conf4build/copyAabWhenBundleFinish.gradle")

val confProp = Properties()
file("${rootProject.projectDir}/conf4build/buildConf.properties").inputStream().use {
  confProp.load(it)
}

android {
  namespace = "com.example.scan"
  compileSdk = 35

  defaultConfig {
    applicationId = "com.example.scan"
    minSdk = 23
    targetSdk = 34
    versionCode = 666
    versionName = "6.6.6"

    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    vectorDrawables {
      useSupportLibrary = true
    }

    ndk {
      abiFilters.addAll(setOf("arm64-v8a", "armeabi-v7a", "x86", "x86_64"))
    }
  }

  signingConfigs {
    create("internal") {
      storeFile = rootProject.file("scan_app_test_sign")
      storePassword = "test_sign"
      keyAlias = "test_sign"
      keyPassword = "test_sign"
    }
  }

  buildTypes {
    create("internal") {
      isMinifyEnabled = true
      isShrinkResources = true
      signingConfig = signingConfigs.getByName("internal")
      proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
      configure<CrashlyticsExtension> {
        mappingFileUploadEnabled = false
      }
    }
  }
  compileOptions {
    isCoreLibraryDesugaringEnabled = true
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
  }
  kotlin {
    jvmToolchain(17)
    sourceSets {
      all {
        languageSettings {
          optIn("androidx.compose.material3.ExperimentalMaterial3Api")
          optIn("androidx.compose.foundation.ExperimentalFoundationApi")
          optIn("androidx.compose.foundation.layout.ExperimentalLayoutApi")
          optIn("androidx.compose.ui.text.ExperimentalTextApi")
          optIn("com.google.accompanist.permissions.ExperimentalPermissionsApi")
          optIn("kotlinx.coroutines.DelicateCoroutinesApi")
          optIn("org.orbitmvi.orbit.annotation.OrbitExperimental")
        }
      }
    }
  }
  buildFeatures {
    compose = true
    buildConfig = true
    viewBinding = true
  }
  composeOptions {
  }
  packaging {
    resources {
      excludes += "/META-INF/{AL2.0,LGPL2.1}"
    }
  }
  kapt {
    arguments {
      arg("objectbox.myObjectBoxPackage", "com.example.scan")
    }
  }
}

dependencies {

  coreLibraryDesugaring(libs.desugar.jdk.libs)
  implementation(libs.relinker)

  implementation(libs.androidx.core.ktx)
  implementation(libs.androidx.startup.runtime)
//  implementation(libs.androidx.work.runtime)
  implementation(libs.androidx.lifecycle.runtime.ktx)
  implementation(libs.androidx.lifecycle.runtime.compose)
  implementation(libs.androidx.lifecycle.viewmodel.ktx)
  implementation(libs.androidx.lifecycle.viewmodel.compose)
  implementation(libs.androidx.lifecycle.process)
  implementation(libs.androidx.activity.compose)
  implementation(platform(libs.androidx.compose.bom))
  implementation(libs.androidx.ui)
  implementation(libs.androidx.ui.viewbinding)
  implementation(libs.androidx.ui.graphics)
  implementation(libs.androidx.ui.tooling.preview)
  implementation(libs.androidx.material.icons.extended)
  implementation(libs.androidx.material3)

  implementation(libs.androidx.cardview)
  implementation(libs.androidx.constraintlayout)

  implementation(libs.accompanist.permissions)

  implementation(libs.barcode.scanning)
  implementation(libs.androidx.camera.camera2)
  implementation(libs.androidx.camera.lifecycle)
  implementation(libs.androidx.camera.view)
//  implementation(libs.capturable)
  implementation(libs.capturecomposable)
  implementation(libs.qrose)
//  implementation(libs.qrose.oned)
  implementation(libs.compose.webview.multiplatform)
  implementation(libs.bottomsheetdialog.compose)
  implementation(libs.revealswipe)

  implementation(libs.ez.vcard)
//  implementation(libs.wifiutils)

  implementation(libs.resaca)
  implementation(libs.resacakoin)

  implementation(platform(libs.koin.bom))
  implementation(libs.koin.core)
  implementation(libs.koin.android)
  implementation(libs.koin.androidx.compose)

  testImplementation(libs.junit)
  androidTestImplementation(libs.androidx.junit)
  androidTestImplementation(libs.androidx.espresso.core)
  androidTestImplementation(platform(libs.androidx.compose.bom))
  androidTestImplementation(libs.androidx.ui.test.junit4)
  debugImplementation(libs.androidx.ui.tooling)
  debugImplementation(libs.androidx.ui.test.manifest)

  implementation(libs.kache)
  implementation(libs.mmkv)

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)
  implementation(libs.kotlinx.datetime)
  implementation(libs.kotlinx.serialization.json)

  implementation(libs.kronos)

  implementation(libs.guia)

  implementation(libs.lottie.compose)

  implementation(libs.lyricist)
  ksp(libs.lyricist.processor)

  implementation(libs.orbit.viewmodel)
  implementation(libs.orbit.compose)

  implementation(libs.kermit)

  implementation(libs.coil.compose)
  implementation(libs.compressor)

  implementation(libs.multiplatform.markdown.renderer.m3)
  implementation(libs.multiplatform.markdown.renderer.coil2)

  ksp(libs.ktorfit.ksp)
  implementation(libs.ktorfit.lib.light)
  implementation(libs.ktorfit.converters.response)
  implementation(libs.ktor.client.okhttp)
  implementation(libs.ktor.client.content.negotiation)
  implementation(libs.ktor.serialization.kotlinx.json)

  implementation(libs.commons.codec)
//  implementation(libs.commons.io)

  implementation(platform(libs.firebase.bom))
  implementation(libs.firebase.crashlytics)
  implementation(libs.firebase.analytics)
  implementation(libs.firebase.config)
  implementation(libs.firebase.messaging)

  implementation(libs.review.ktx)
  implementation(libs.installreferrer)
  implementation(libs.play.services.base)
  implementation(libs.play.services.ads.identifier)
  implementation(libs.play.services.ads)
  implementation(libs.user.messaging.platform)

  implementation("com.applovin:applovin-sdk:13.3.0")

  implementation(libs.tenjin)
}

ksp {
  arg("lyricist.generateStringsProperty", "true")
}