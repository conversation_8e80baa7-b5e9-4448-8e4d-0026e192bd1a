[versions]
accompanist = "0.37.3"
agp = "8.9.1"
barcodeScanning = "17.3.0"
bottomsheetdialogCompose = "1.6.0"
camera2 = "1.4.2"
capturecomposable = "1.0.2"
cardview = "1.0.0"
coil = "2.7.0"
commonsIo = "2.18.0"
composeWebviewMultiplatform = "2.0.0"
compressor = "3.0.1"
constraintlayout = "2.2.1"
crashlytics = "3.0.4"
desugarJdkLibs = "2.1.5"
ezVcard = "0.12.1"
firebaseBom = "33.15.0"
guia = "1.0.0-beta05"
googleServices = "4.4.2"
kache = "2.1.1"
kermit = "2.0.6"
kotlin = "2.1.21"
kronos = "0.0.1"
ksp = "2.1.21-2.0.1"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
kotlinxCoroutinesCore = "1.10.2"
kotlinxDatetime = "0.6.2"
kotlinxSerialization = "1.8.1"
ktor = "3.1.2"
ktorfit-ksp = "2.5.1"
ktorfit = "2.5.2"
lottie = "6.6.7"
lyricist = "1.7.0"
mmkv = "1.3.14"
multiplatformMarkdownRenderer = "0.35.0"
objectboxGradlePlugin = "4.2.0"
orbit = "10.0.0"
lifecycle = "2.9.1"
activityCompose = "1.10.1"
composeBom = "2025.06.00"
playServicesAds = "24.4.0"
playServicesAdsIdentifier = "18.2.0"
playServicesBase = "18.7.0"
qrose = "1.0.1"
relinker = "1.4.5"
resaca = "4.4.6"
koinBom = "4.1.0"
revealswipe = "3.0.0"
reviewKtx = "2.0.2"
startupRuntime = "1.2.0"
uiTextGoogleFonts = "1.8.2"
installreferrer = "2.2"
userMessagingPlatform = "3.2.0"
wifiutils = "1.6.6"
tenjin = "1.16.7"
workRuntime = "2.10.1"

[libraries]
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camera2" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camera2" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "camera2" }
androidx-cardview = { module = "androidx.cardview:cardview", version.ref = "cardview" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-startup-runtime = { module = "androidx.startup:startup-runtime", version.ref = "startupRuntime" }
androidx-ui-text-google-fonts = { module = "androidx.compose.ui:ui-text-google-fonts", version.ref = "uiTextGoogleFonts" }
androidx-ui-viewbinding = { module = "androidx.compose.ui:ui-viewbinding" }
androidx-work-runtime = { module = "androidx.work:work-runtime", version.ref = "workRuntime" }
barcode-scanning = { module = "com.google.mlkit:barcode-scanning", version.ref = "barcodeScanning" }
bottomsheetdialog-compose = { module = "com.holix.android:bottomsheetdialog-compose", version.ref = "bottomsheetdialogCompose" }
capturecomposable = { module = "com.github.yonghanJu:CaptureComposable", version.ref = "capturecomposable" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coil" }
commons-codec = { module = "commons-codec:commons-codec", version = "1.18.0" }
commons-io = { module = "commons-io:commons-io", version.ref = "commonsIo" }
compose-webview-multiplatform = { module = "io.github.kevinnzou:compose-webview-multiplatform", version.ref = "composeWebviewMultiplatform" }
compressor = { module = "id.zelory:compressor", version.ref = "compressor" }
desugar_jdk_libs = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugarJdkLibs" }
ez-vcard = { module = "com.googlecode.ez-vcard:ez-vcard", version.ref = "ezVcard" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-config = { group = "com.google.firebase", name = "firebase-config" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
guia = { module = "com.roudikk.guia:guia", version.ref = "guia" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
kache = { module = "com.mayakapps.kache:kache", version.ref = "kache" }
kermit = { module = "co.touchlab:kermit", version.ref = "kermit" }
koin-bom = { group = "io.insert-koin", name = "koin-bom", version.ref = "koinBom" }
koin-core = { group = "io.insert-koin", name = "koin-core" }
koin-android = { group = "io.insert-koin", name = "koin-android" }
koin-androidx-compose = { group = "io.insert-koin", name = "koin-androidx-compose" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesCore" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
kronos = { module = "io.github.softartdev:kronos", version.ref = "kronos" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktorfit-ksp = { module = "de.jensklingenberg.ktorfit:ktorfit-ksp", version.ref = "ktorfit-ksp" }
ktorfit-lib-light = { module = "de.jensklingenberg.ktorfit:ktorfit-lib-light", version.ref = "ktorfit" }
ktorfit-converters-response	 = { module = "de.jensklingenberg.ktorfit:ktorfit-converters-response", version.ref = "ktorfit" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }
lyricist = { module = "cafe.adriel.lyricist:lyricist", version.ref = "lyricist" }
lyricist-processor = { module = "cafe.adriel.lyricist:lyricist-processor", version.ref = "lyricist" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
multiplatform-markdown-renderer-m3 = { module = "com.mikepenz:multiplatform-markdown-renderer-m3", version.ref = "multiplatformMarkdownRenderer" }
multiplatform-markdown-renderer-coil2 = { module = "com.mikepenz:multiplatform-markdown-renderer-coil2", version.ref = "multiplatformMarkdownRenderer" }
objectbox-gradle-plugin = { module = "io.objectbox:objectbox-gradle-plugin", version.ref = "objectboxGradlePlugin" }
orbit-compose = { module = "org.orbit-mvi:orbit-compose", version.ref = "orbit" }
orbit-viewmodel = { module = "org.orbit-mvi:orbit-viewmodel", version.ref = "orbit" }
play-services-ads = { module = "com.google.android.gms:play-services-ads", version.ref = "playServicesAds" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "playServicesAdsIdentifier" }
play-services-base = { module = "com.google.android.gms:play-services-base", version.ref = "playServicesBase" }
qrose = { module = "io.github.alexzhirkevich:qrose", version.ref = "qrose" }
qrose-oned = { module = "io.github.alexzhirkevich:qrose-oned", version.ref = "qrose" }
relinker = { module = "com.getkeepsafe.relinker:relinker", version.ref = "relinker" }
resaca = { module = "io.github.sebaslogen:resaca", version.ref = "resaca" }
resacakoin = { module = "io.github.sebaslogen:resacakoin", version.ref = "resaca" }
revealswipe = { module = "de.charlex.compose:revealswipe", version.ref = "revealswipe" }
review-ktx = { module = "com.google.android.play:review-ktx", version.ref = "reviewKtx" }
tenjin = { module = "com.tenjin:android-sdk", version.ref = "tenjin" }
installreferrer = { group = "com.android.installreferrer", name = "installreferrer", version.ref = "installreferrer" }
user-messaging-platform = { module = "com.google.android.ump:user-messaging-platform", version.ref = "userMessagingPlatform" }
wifiutils = { module = "io.github.thanosfisherman.wifiutils:wifiutils", version.ref = "wifiutils" }


[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ktorfit = { id = "de.jensklingenberg.ktorfit", version.ref = "ktorfit" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }

